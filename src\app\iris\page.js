"use client";

import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import dynamic from "next/dynamic";
import Image from "next/image";
import "./iris.css";

// Dynamically import Plyr to avoid SSR issues
const Plyr = dynamic(() => import("plyr-react"), {
  ssr: false,
  loading: () => <div>Loading video player...</div>
});

// Import Plyr CSS
import "plyr-react/plyr.css";

const IrisPage = () => {
  const controls = [
    "play-large",
    "play",
    "progress",
    "current-time",
    "mute",
    "captions",
    "pip",
    "airplay",
    "fullscreen",
  ];

  const eventTrackerGa4 = (category, action, label, nonInteraction) => {
    // GA4 tracking can be implemented here if needed
    console.log("GA4 Event:", { category, action, label, nonInteraction });
  };

  return (
    <div className="iris-minds">
      {/* Hero Section */}
      <div className="hero">
        <div className="container">
          <div className="row d-flex justify-content-center header">
            <div className="col-12 col-md-7 text-center">
              <h1 className="h2">
                IRIS - Interactive Reading & Immersive Stories
              </h1>
              <p className="lead text-white">
                Boost your child's reading skills with interactive stories and engaging adventures.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Content Container */}
      <div className="iris-content-box">
        <div className="container" id="player-container">
          {/* Video Player */}
          <div className="row d-flex justify-content-center">
            <div className="col-12 col-md-8">
              <div className="player-container">
                <Plyr
                  id="plyr"
                  controls
                  options={{ volume: 0.1, controls }}
                  source={{
                    type: "video",
                    sources: [
                      {
                        src: "https://www.youtube.com/watch?v=yrgI2U9hAc8",
                        provider: "youtube",
                      },
                    ],
                  }}
                />
              </div>
            </div>
          </div>

          {/* Content Blocks */}
          <div className="row container-block mt-7">
            <div className="col-12 col-md-6 block-img d-flex justify-content-start">
              <Image src="/images/iris/iris-why-play.png" alt="Why Play IRIS" width={400} height={300} />
            </div>
            <div className="col-12 col-md-6">
              <h2>Why choose IRIS?</h2>
              <p>
                IRIS combines the magic of storytelling with interactive learning to help children
                develop strong reading comprehension, vocabulary, and critical thinking skills.
              </p>
            </div>
          </div>

          <div className="row container-block mt-7">
            <div className="col-12 col-md-6">
              <h2>How does IRIS work?</h2>
              <p>
                Through immersive stories, interactive quizzes, and engaging activities,
                IRIS makes reading fun and educational for children of all ages.
              </p>
            </div>
            <div className="col-12 col-md-6 block-img d-flex justify-content-end">
              <Image src="/images/iris/iris-how-it-works.png" alt="How IRIS works" width={400} height={300} />
            </div>
          </div>
        </div>
      </div>

      {/* Ribbon Section */}
      <div className="iris-ribbon-block">
        <div className="iris-ribbon"></div>
      </div>

      {/* FIKS Container */}
      <div className="iris-fiks-container">
        <div className="iris-fiks-container-img">
          <Image src="/images/iris/iris-fiks-logo.png" alt="FIKS Logo" width={330} height={200} />
        </div>
        <div>
          <h2>Powered by FIKS</h2>
          <p>
            IRIS is powered by FIKS (Fun Interactive Knowledge System), ensuring
            high-quality educational content that adapts to your child's learning pace.
          </p>
        </div>
      </div>

      {/* Additional Content */}
      <div className="container iris-minds mt-7">
        <div className="row container-block">
          <div className="col-12 col-md-6 block-img d-flex justify-content-start">
            <Image src="/images/iris/iris-progress.png" alt="Progress Tracking" width={400} height={300} />
          </div>
          <div className="col-12 col-md-6">
            <h2>Track Progress & Growth</h2>
            <p>
              Monitor your child's reading progress with detailed analytics and
              personalized recommendations for continued learning.
            </p>
          </div>
        </div>

        {/* Testimonials Carousel */}
        <div className="row mt-3">
          <div className="col-12">
            <div className="iris-carousel mt-7">
              <Carousel
                infiniteLoop={true}
                autoPlay={true}
                showStatus={false}
                showIndicators={false}
                swipeable={false}
                renderArrowPrev={(clickHandler, hasPrev) => {
                  return (
                    <div className="arrow left-arrow" onClick={clickHandler}>
                      <button className="btn btn-primary">
                        <Image src="/images/iris/left-arrow.png" alt="Left" width={20} height={20} />
                      </button>
                    </div>
                  );
                }}
                renderArrowNext={(clickHandler, hasNext) => {
                  return (
                    <div className="arrow right-arrow" onClick={clickHandler}>
                      <button className="btn btn-primary">
                        <Image src="/images/iris/right-arrow.png" alt="Right" width={20} height={20} />
                      </button>
                    </div>
                  );
                }}
              >
                {/* Testimonial 1 */}
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      <Image src="/images/iris/review-two.png" className="left" alt="Left" width={230} height={200} />
                      <Image src="/images/iris/review-one.png" className="center" alt="Center" width={280} height={250} />
                      <Image src="/images/iris/review-three.png" className="right" alt="Right" width={230} height={200} />
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                      "IRIS has transformed my child's reading experience. The interactive
                      stories keep them engaged while building essential literacy skills."
                      <br /> <br />- Jennifer Smith, Parent
                    </p>
                  </div>
                </div>

                {/* Testimonial 2 */}
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      <Image src="/images/iris/review-one.png" className="left" alt="Left" width={230} height={200} />
                      <Image src="/images/iris/review-two.png" className="center" alt="Center" width={280} height={250} />
                      <Image src="/images/iris/review-three.png" className="right" alt="Right" width={230} height={200} />
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                      "As a teacher, I love how IRIS combines entertainment with education.
                      My students are more excited about reading than ever before."
                      <br /> <br />- Ms. Rodriguez, Elementary Teacher
                    </p>
                  </div>
                </div>

                {/* Testimonial 3 */}
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      <Image src="/images/iris/review-two.png" className="left" alt="Left" width={230} height={200} />
                      <Image src="/images/iris/review-three.png" className="center" alt="Center" width={280} height={250} />
                      <Image src="/images/iris/review-two.png" className="right" alt="Right" width={230} height={200} />
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                      "The progress tracking feature helps me understand exactly where
                      my child needs support in their reading journey."
                      <br /> <br />- Dr. Williams, Child Development Specialist
                    </p>
                  </div>
                </div>
              </Carousel>
            </div>
          </div>
        </div>

        {/* Games Section */}
        <div className="row d-flex justify-content-center mt-7">
          <span className="games-heading">Explore IRIS in Top SKIDOS Games</span>
          <div className="games-block">
            <div className="games">
              <Image
                width={200}
                height={160}
                src="/images/iris/reading-adventure.png"
                alt="Reading Adventure"
              />
              <span>Reading Adventure</span>
              <a href="https://apps.apple.com/us/app/reading-adventure-games/id1506886061">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "IRIS",
                      "Download",
                      "Reading Adventure",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
            <div className="games">
              <Image width={200} height={160} src="/images/iris/story-builder.png" alt="Story Builder" />
              <span>Story Builder</span>
              <a href="https://apps.apple.com/us/app/story-builder-games/id1506886061">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "IRIS",
                      "Download",
                      "Story Builder",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
          </div>
        </div>

        {/* Certification Section */}
        <div className="row container-block d-flex justify-content-center mt-7">
          <span className="games-heading">
            Safe & Educational Learning Environment
          </span>
          <div className="certification-block">
            <div className="certification">
              <Image src="/images/iris/certification-one.png" alt="IRIS Certification" width={150} height={150} />
              <p className="mt-2">Educational Standards Compliant</p>
            </div>
            <div className="certification">
              <Image src="/images/iris/certification-two.png" alt="IRIS Certification" width={150} height={150} />
              <p className="mt-2">Designed by Reading Specialists</p>
            </div>
            <div className="certification">
              <Image src="/images/iris/certification-three.png" alt="IRIS Certification" width={150} height={150} />
              <p className="mt-2">Age-Appropriate Content</p>
            </div>
            <div className="certification">
              <Image src="/images/iris/certification-four.png" alt="IRIS Certification" width={150} height={150} />
              <p className="mt-2">Privacy Protected</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IrisPage;
