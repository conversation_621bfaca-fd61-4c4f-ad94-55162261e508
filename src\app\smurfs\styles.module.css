
/* ===== GLOBAL STYLES ===== */

/* Page Wrapper - Matches home page approach */
.smurfsPageWrapper {
  max-width: 1728px;
  margin: 0 auto;
  background-color: #f7f0ea;
}

/* FIRST FRAME - Hero Banner */
.firstFrame {
  width: 100%;
  position: relative;
  overflow: hidden;
  background-color: #f7f0ea;
}

/* Desktop Banner */
.desktopBanner {
  display: block;
  width: 100%;
  height: clamp(300px, 42vw, 720px);
  position: relative;
  overflow: hidden;
}

/* Mobile Banner */
.mobileBanner {
  display: none;
  width: 100%;
  height: clamp(450px, 80vw, 650px);
  position: relative;
  overflow: hidden;
}

/* Banner Image */
.bannerImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

/* SECOND FRAME - Content Section */
.secondFrame {
  background: #FFFFFF;
  padding: clamp(2rem, 8vw, 5rem) 0;
  width: 100%;
}

.secondFrameContent {
  padding: 0 clamp(1.5rem, 5vw, 3rem);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

/* Typography */
.mainHeading {
  font-size: clamp(2rem, 6vw, 4rem);
  font-weight: 700;
  font-family: var(--font-poppins);
  color: #EA73C0;
  margin: 0 0 1rem 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  text-align: left;
  line-height: 1.2;
}

.subHeading {
  font-size: clamp(1rem, 3vw, 1.8rem);
  font-family: var(--font-poppins);
  margin: 0 0 1.5rem 0;
  color: #949494;
  text-align: left;
  font-weight: 500;
}

.description {
  font-size: clamp(1rem, 2.5vw, 1.4rem);
  color: #86868B;
  font-family: var(--font-poppins);
  line-height: 1.8;
  margin: 0 0 2rem 0;
  width: 100%;
  text-align: left;
  font-weight: 600;
}

/* THIRD FRAME - Game Screenshots */
.thirdFrame {
  padding: 0 0 clamp(2rem, 8vw, 5rem) 0;
  width: 100%;
}

.thirdFrameContent {
  padding: 0 clamp(1.5rem, 5vw, 3rem);
  box-sizing: border-box;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.thirdFrameContent::-webkit-scrollbar {
  display: none;
}

.gamesContainer {
  display: flex;
  gap: clamp(0.75rem, 3vw, 1.25rem);
  padding: 0;
  width: max-content;
  min-width: 100%;
}

.gameCard {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  width: clamp(280px, 50vw, 560px);
  height: clamp(210px, 37.5vw, 420px);
  transition: transform 0.3s ease;
  flex-shrink: 0;
  min-width: 280px;
}

.gameCard:hover {
  transform: translateY(-5px);
}

.gameCard img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/* ===== RESPONSIVE STYLES ===== */

/* Mobile: Switch to mobile banner and center content */
@media only screen and (max-width: 768px) {
  .desktopBanner {
    display: none;
  }

  .mobileBanner {
    display: block;
    height: clamp(400px, 75vw, 600px);
  }

  .secondFrameContent {
    align-items: center;
    text-align: center;
  }

  .mainHeading,
  .subHeading,
  .description {
    text-align: center;
  }
}
