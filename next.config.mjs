/** @type {import('next').NextConfig} */
import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin("./src/utils/i18n/request.js");

const nextConfig = {
  images: {
    domains: ["stg.skidos.com", "skidos.com"],
    formats: ["image/avif", "image/webp"],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    unoptimized: true,
  },
  trailingSlash: true,
  // async rewrites() {
  //   const dynamicRewrites = [];

  //   for (const key in process.env) {
  //     if (key.startsWith("NEXT_PUBLIC_REWRITE_SOURCE_")) {
  //       const sourceKey = key;
  //       const destinationKey = key.replace("_SOURCE_", "_DESTINATION_");

  //       const source = process.env[sourceKey];
  //       const destination = process.env[destinationKey];

  //       if (source && destination) {
  //         dynamicRewrites.push({
  //           source,
  //           destination,
  //         });
  //       }
  //     }
  //   }

  //   return dynamicRewrites;
  // },
  async redirects() {
    return [
      {
        source: "/blog/",
        destination: "/blogs/",
        permanent: true,
      },
      {
        source: "/article/",
        destination: "/news/",
        permanent: true,
      },
      {
        source: "/blog/:slug/",
        destination: "/blogs/:slug/",
        permanent: true,
      },
      {
        source: "/article/:slug/",
        destination: "/news/:slug/",
        permanent: true,
      },
      {
        source: "/blog/:slug/:path*",
        destination: "/blogs/:slug/:path*",
        permanent: true,
      },
      {
        source: "/article/:slug/:path*",
        destination: "/news/:slug/:path*",
        permanent: true,
      },
      {
        source: "/portfolio/:slug/",
        destination: "/products/",
        permanent: true,
      },
    ];
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};

export default withNextIntl(nextConfig);
