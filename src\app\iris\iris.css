.iris-minds .container-block .block-img img {
  width: 300px;
}

.iris-minds .player-container {
  margin-top: -180px;
  padding: 25px;
}

.iris-minds .player-container .plyr {
  border-radius: 15px;
  border: 3px solid #fff;
}

.iris-minds .container-block {
  align-items: center;
  justify-content: center;
}

.iris-minds .hero iframe {
  position: absolute;
}

.iris-minds h2,
.iris-minds .h2 {
  font-weight: 700;
  font-size: 2.1rem;
  margin-top: 23px;
}

.iris-fiks-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 5rem;
  margin-bottom: 4rem;
}

.iris-fiks-container > div {
  padding: 0 3rem;
  width: 40%;
  align-items: center;
}

.iris-fiks-container-img img {
  width: 330px;
}

.container-block p {
  font-weight: 500;
}

.iris-minds .iris-ribbon-block {
  position: relative;
  margin-bottom: 350px;
  margin-top: 30px;
}
.iris-minds .iris-ribbon {
  position: absolute;
  background-image: url(/images/iris/iris-flyer.png);
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 225px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}
.iris-minds .mt-7 {
  margin-top: 5rem;
}
.iris-minds .hero {
  height: 680px;
  width: 100%;
  background-image: url(/images/iris/iris-landing-header.png);
  background-size: cover;  
  background-position: center center; 
}
.iris-content-box{
  background-image: url(/images/iris/iris-content-background.png);
  background-size: cover; 
}
.iris-minds .hero .header {
  padding-top: 97px;
  position: relative;
}
.iris-minds .btn-primary,
.btn-ghost {
  position: relative;
  background-color: #f66007;
  outline: none;
  padding: 15px 25px;
  border: 1px solid #fff;
  font-weight: 700;
  border-radius: 20px;
}
.iris-minds .btn-primary:hover,
.btn-ghost:hover {
  border: 1px solid rgba(153, 153, 153, 0.753);
}
.btn-ghost {
  background-color: transparent;
  border: 1px solid #333;
  color: #333;
  margin-right: 15px;
  padding-right: 35px;
}
.btn-ghost img {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 7px;
}
.iris-minds .certification-block {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-top: 5rem;
  margin-bottom: 4rem;
}
.iris-minds .certification-block > div {
  padding: 0 3rem;
  width: 40%;
  align-items: center;
}
.iris-minds .certification-block img {
  width: 330px;
}
.iris-minds .right-block {
  margin-top: -64px;
}
.iris-minds .games {
  background-color: #fff;
  border-radius: 20px;
  padding: 20px;
  margin: 10px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
  cursor: pointer;
}

.iris-minds .games img{
  display: block
}

.iris-minds .games a{
  text-decoration: none;
}

.iris-minds .games:hover{
  box-shadow: 0 4px 8px rgba(93, 93, 93, 0.3);
  transition: box-shadow 400ms ease-in-out;
}

.iris-minds .games span{
  font-weight: 700;
  display: inline-block;
  font-size: 20px;
  margin: 0.5rem 0;
  text-align: left !important;
}

.iris-minds .games button{
background-color: #F66007;
display: block;
width: 100%;
border-radius: 4px;
height: 48px;
font-size: 1.3rem;
font-weight: 700;
outline: none;
border: none;
color: #ffffff
}
.iris-minds .iris-carousel {
  background-color: #00c6a3;
  border-radius: 20px;
}
.iris-minds .carousel-root {
  padding-top: 50px;
}
.iris-minds .iris-carousel .carousel .review-image {
  position: relative;
  margin-top: -50px;
  margin-bottom: 50px;
}
.iris-minds .iris-carousel .carousel .review-image .left {
  position: absolute;
  left: 50px;
  top: 50px;
  z-index: 1;
}
.iris-minds .iris-carousel .carousel .review-image .center {
  position: relative;
  z-index: 3;
  margin: 0 auto;
  display: block;
}
.iris-minds .iris-carousel .carousel .review-image .right {
  position: absolute;
  right: 50px;
  top: 50px;
  z-index: 2;
}
.iris-minds .iris-carousel .carousel .review-image img {
  width: 300px;
  border-radius: 20px;
}
.iris-minds .iris-carousel .btn-primary {
  background-color: #f66007;
  border-radius: 20px;
  padding: 15px 25px;
  text-align: center;
  color: #fff;
  font-weight: 700;
  border: none;
  margin-bottom: 50px;
}
.iris-minds .iris-carousel .btn-primary:hover {
  background-color: #e55a06;
}
.iris-minds .iris-carousel .carousel .slide {
  background: transparent;
}
.iris-minds .iris-carousel .carousel .control-dots {
  bottom: -50px;
}
.iris-minds .iris-carousel .carousel .control-dots .dot {
  background: #fff;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 0 5px;
  opacity: 0.5;
}
.iris-minds .iris-carousel .carousel .control-dots .dot.selected {
  opacity: 1;
}

/* Mobile Responsive Styles */
@media only screen and (max-width: 450px) {
  .iris-minds h2,
  .iris-minds .h2 {
    margin-top: 25px;
  }
  .iris-minds .mt-7 {
    margin-top: 2rem;
  }
  .iris-minds .iris-ribbon-block {
    margin-bottom: 265px;
    margin-top: 20px;
  }
  .iris-minds .iris-carousel .carousel .review-image .right {
    right: 5px;
  }
  .iris-minds .iris-carousel .carousel .review-image {
    transform: scale(0.9);
    margin-top: -15px;
  }
  .iris-minds .carousel-root {
    padding-top: 25px;
  }
  .iris-minds .iris-minds .right-block {
    margin-top: -10px;
    padding-bottom: 70px;
  }
  .iris-minds .player-container {
    padding: 5px;
    margin-top: -90px;
  }
  .iris-content-box{
      background-image: url(/images/iris/iris-content-background-mb.png);
      background-size: auto;
      background-repeat: no-repeat;
    }
    .iris-minds .hero {
      background-image: url(/images/iris/iris-landing-header-mb.png);
      background-position: center center;
      background-size: cover;
      height: 468px;
    }
    .iris-fiks-container{
      flex-direction: column;
      margin-bottom: 0rem;
    }
    .iris-fiks-container > div{
      padding: 0;
      width: 100%;
    }
    .iris-fiks-container-img{
      display: flex;
      justify-content: center;
    }
}

/* iPad */
@media all and (device-width: 1024px) and (device-height: 768px) and (orientation: landscape) {
  .iris-minds .navbar .nav-link {
    margin: 0 5px;
  }
}

/* iPad Pro*/
@media all and (device-width: 1366px) and (device-height: 1024px) and (orientation: landscape) {
}
