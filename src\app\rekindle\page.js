"use client";

import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import dynamic from "next/dynamic";
import Image from "next/image";
import "./rekindle.css";

// Dynamically import Plyr to avoid SSR issues
const Plyr = dynamic(() => import("plyr-react"), {
  ssr: false,
  loading: () => <div>Loading video player...</div>
});

// Import Plyr CSS
import "plyr-react/plyr.css";

const RekindlePage = () => {
  const controls = [
    "play-large",
    "play",
    "progress",
    "current-time",
    "mute",
    "captions",
    "pip",
    "airplay",
    "fullscreen",
  ];

  const eventTrackerGa4 = (category, action, label, nonInteraction) => {
    // GA4 tracking can be implemented here if needed
    console.log("GA4 Event:", { category, action, label, nonInteraction });
  };

  return (
    <div className="rekindle-minds">
      {/* Hero Section */}
      <div className="hero">
        <iframe
          src="https://images.skidos.com/rekindle/landing-page.html"
          width="100%"
          height="680px"
          frameBorder="0"
          title="Rekindle Minds"
        >
          Browser not compatible.
        </iframe>
        <div className="container">
          <div className="row d-flex justify-content-center header">
            <div className="col-12 col-md-7 text-center">
              <h1 className="h2">
                A mindful game designed by teachers to boost your child's
                emotional growth and learning
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Content Container */}
      <div className="container" id="player-container">
        {/* Video Player */}
        <div className="row d-flex justify-content-center">
          <div className="col-12 col-md-8">
            <div className="player-container">
              <Plyr
                id="plyr"
                controls
                options={{ volume: 0.1, controls }}
                source={{
                  type: "video",
                  sources: [
                    {
                      src: "https://www.youtube.com/watch?v=yrgI2U9hAc8",
                      provider: "youtube",
                    },
                  ],
                }}
              />
            </div>
          </div>
        </div>

        {/* Content Blocks */}
        <div className="row container-block mt-7">
          <div className="col-12 col-md-6 block-img d-flex justify-content-start">
            <Image src="/images/rekindle-minds/why-play.png" alt="Why Play Rekindle Minds" width={400} height={300} />
          </div>
          <div className="col-12 col-md-6">
            <h2>Why play Rekindle Minds?</h2>
            <p>
              Rekindle Minds takes children on a playful deep-dive into their
              emotions. This delightful interactive game helps them build strong
              relationships, manage stress and develop a positive, confident
              personality.
            </p>
          </div>
        </div>

        <div className="row container-block mt-7">
          <div className="col-12 col-md-6">
            <h2>How does it work?</h2>
            <p>
              Explore, learn and grow with 150+ immersive stories, quizzes,
              puzzles, and games - designed by trusted educators to develop your
              child's social and emotional skills.
            </p>
          </div>
          <div className="col-12 col-md-6 block-img d-flex justify-content-end">
            <Image src="/images/rekindle-minds/frame-28.png" alt="How it works" width={400} height={300} />
          </div>
        </div>
      </div>

      {/* Ribbon Section */}
      <div className="ribbon-block">
        <div className="ribbon"></div>
      </div>

      {/* Additional Content */}
      <div className="container rekindle-minds mt-7">
        <div className="row container-block">
          <div className="col-12 col-md-6 block-img d-flex justify-content-start">
            <Image src="/images/rekindle-minds/progress-report.png" alt="Progress Report" width={400} height={300} />
          </div>
          <div className="col-12 col-md-6">
            <h2>Expert-approved guide to SEL</h2>
            <p>
              Rekindle Minds is designed by teachers and child psychologists to
              help children develop social and emotional skills through play.
            </p>
          </div>
        </div>

        {/* Testimonials Carousel */}
        <div className="row mt-3">
          <div className="col-12">
            <div className="rekindle-carousel mt-7">
              <Carousel
                infiniteLoop={true}
                autoPlay={true}
                showStatus={false}
                showIndicators={false}
                swipeable={false}
                renderArrowPrev={(clickHandler, hasPrev) => {
                  return (
                    <div className="arrow left-arrow" onClick={clickHandler}>
                      <button className="btn btn-primary">
                        <Image src="/images/rekindle-minds/left-arrow.png" alt="Left" width={20} height={20} />
                      </button>
                    </div>
                  );
                }}
                renderArrowNext={(clickHandler, hasNext) => {
                  return (
                    <div className="arrow right-arrow" onClick={clickHandler}>
                      <button className="btn btn-primary">
                        <Image src="/images/rekindle-minds/right-arrow.png" alt="Right" width={20} height={20} />
                      </button>
                    </div>
                  );
                }}
              >
                {/* Testimonial 1 */}
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      <Image src="/images/rekindle-minds/review-two.png" className="left" alt="Left" width={230} height={200} />
                      <Image src="/images/rekindle-minds/review-one.png" className="center" alt="Center" width={280} height={250} />
                      <Image src="/images/rekindle-minds/review-three.png" className="right" alt="Right" width={230} height={200} />
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                      "Love how this app helps the young ones understand their
                      feelings! My daughter Zoey is very sensitive so she likes
                      this game a lot! She loves playing with the character
                      Rekindle and learning about emotions."
                      <br /> <br />- Sarah Johnson, Parent
                    </p>
                  </div>
                </div>

                {/* Testimonial 2 */}
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      <Image src="/images/rekindle-minds/review-one.png" className="left" alt="Left" width={230} height={200} />
                      <Image src="/images/rekindle-minds/review-two.png" className="center" alt="Center" width={280} height={250} />
                      <Image src="/images/rekindle-minds/review-three.png" className="right" alt="Right" width={230} height={200} />
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                      "Game-changer! This app teaches children to understand and
                      express their emotions in a healthy way."
                      <br /> <br />- Maria Adamms, School Counselor
                    </p>
                  </div>
                </div>

                {/* Testimonial 3 */}
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      <Image src="/images/rekindle-minds/review-two.png" className="left" alt="Left" width={230} height={200} />
                      <Image src="/images/rekindle-minds/review-three.png" className="center" alt="Center" width={280} height={250} />
                      <Image src="/images/rekindle-minds/review-two.png" className="right" alt="Right" width={230} height={200} />
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                      "The interactive videos are a wonderful way to help guide
                      kids on what is right or wrong."
                      <br /> <br />- Dr. Thor Mitch, Child Psychologist
                    </p>
                  </div>
                </div>
              </Carousel>
            </div>
          </div>
        </div>

        {/* Games Section */}
        <div className="row d-flex justify-content-center mt-7">
          <span className="games-heading">Explore Now In Top SKIDOS Games</span>
          <div className="games-block">
            <div className="games">
              <Image
                width={200}
                height={160}
                src="/images/rekindle-minds/bike-racing.png"
                alt="Skidos Games"
              />
              <span>Bike Racing</span>
              <a href="https://apps.apple.com/us/app/bike-racing-games-for-kids/id1506886061">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "Rekindle Minds",
                      "Download",
                      "Bike Racing",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
            <div className="games">
              <Image width={200} height={160} src="/images/rekindle-minds/Doctor.png" alt="Skidos Games" />
              <span>Doctor</span>
              <a href="https://apps.apple.com/us/app/doctor-games-for-kids/id1506886061">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "Rekindle Minds",
                      "Download",
                      "Doctor",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RekindlePage;
