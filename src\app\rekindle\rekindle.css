.rekindle-minds .container-block .block-img img {
  max-width: 100%;
}
.rekindle-minds .player-container {
  margin-top: -150px;
  padding: 25px;
}
.rekindle-minds .player-container .plyr {
  border-radius: 15px;
  border: 1px solid #fff;
}
.rekindle-minds .container-block {
  align-items: center;
  justify-content: center;
}
.rekindle-minds .hero iframe {
  position: absolute;
}
.rekindle-minds h2,
.rekindle-minds .h2 {
  font-weight: 700;
  font-size: 1.75rem;
  margin-top: 15px;
}
.container-block p {
  font-weight: 500;
}
.rekindle-minds .ribbon-block {
  position: relative;
  margin-bottom: 350px;
  margin-top: 30px;
}
.rekindle-minds .ribbon {
  position: absolute;
  background-image: url(/images/rekindle-minds/ribbon.png);
  width: 100%;
  height: 225px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}
.rekindle-minds .mt-7 {
  margin-top: 5rem;
}
.rekindle-minds .hero {
  background-color: rgb(75 232 193);
  height: 680px;
  width: 100%;
}
.rekindle-minds .hero .header {
  padding-top: 170px;
  position: relative;
}
.rekindle-minds .btn-primary,
.btn-ghost {
  position: relative;
  background-color: #f66007;
  outline: none;
  padding: 15px 25px;
  border: 1px solid #fff;
  font-weight: 700;
  border-radius: 20px;
}
.rekindle-minds .btn-primary:hover,
.btn-ghost:hover {
  border: 1px solid rgba(153, 153, 153, 0.753);
}
.btn-ghost {
  background-color: transparent;
  border: 1px solid #333;
  color: #333;
  margin-right: 15px;
  padding-right: 35px;
}
.btn-ghost img {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 7px;
}
.rekindle-minds .certification-block {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-top: 5rem;
  margin-bottom: 4rem;
}
.rekindle-minds .certification-block > div {
  padding: 0 3rem;
  width: 40%;
  align-items: center;
}
.rekindle-minds .certification-block img {
  width: 330px;
}
.rekindle-minds .right-block {
  margin-top: -64px;
}
.rekindle-minds .games {
  background-color: #fff;
  border-radius: 20px;
  padding: 20px;
  margin: 10px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
  cursor: pointer;
}

.rekindle-minds .games img{
  display: block
}

.rekindle-minds .games a{
  text-decoration: none;
}

.rekindle-minds .games:hover{
  box-shadow: 0 4px 8px rgba(93, 93, 93, 0.3);
  transition: box-shadow 400ms ease-in-out;
}

.rekindle-minds .games span{
  font-weight: 700;
  display: inline-block;
  font-size: 20px;
  margin: 0.5rem 0;
  text-align: left !important;
}

.rekindle-minds .games button{
background-color: #F66007;
display: block;
width: 100%;
border-radius: 4px;
height: 48px;
font-size: 1.3rem;
font-weight: 700;
outline: none;
border: none;
color: #ffffff
}
.rekindle-minds .rekindle-carousel {
  background-color: #b877f7;
  border-radius: 20px;
}
.rekindle-minds .carousel-root {
  padding-top: 50px;
}
.rekindle-minds .rekindle-carousel .carousel .review-image {
  position: relative;
  margin-top: -50px;
  margin-bottom: 50px;
}
.rekindle-minds .rekindle-carousel .carousel .review-image .left {
  position: absolute;
  left: 50px;
  top: 50px;
  z-index: 1;
}
.rekindle-minds .rekindle-carousel .carousel .review-image .center {
  position: relative;
  z-index: 3;
  margin: 0 auto;
  display: block;
}
.rekindle-minds .rekindle-carousel .carousel .review-image .right {
  position: absolute;
  right: 50px;
  top: 50px;
  z-index: 2;
}
.rekindle-minds .rekindle-carousel .carousel .review-image img {
  width: 300px;
  border-radius: 20px;
}
.rekindle-minds .rekindle-carousel .btn-primary {
  background-color: #f66007;
  border-radius: 20px;
  padding: 15px 25px;
  text-align: center;
  color: #fff;
  font-weight: 700;
  border: none;
  margin-bottom: 50px;
}
.rekindle-minds .rekindle-carousel .btn-primary:hover {
  background-color: #e55a06;
}
.rekindle-minds .rekindle-carousel .carousel .slide {
  background: transparent;
}
.rekindle-minds .rekindle-carousel .carousel .control-dots {
  bottom: -50px;
}
.rekindle-minds .rekindle-carousel .carousel .control-dots .dot {
  background: #fff;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 0 5px;
  opacity: 0.5;
}
.rekindle-minds .rekindle-carousel .carousel .control-dots .dot.selected {
  opacity: 1;
}

/* Mobile Responsive Styles */
@media only screen and (max-width: 450px) {
  .rekindle-minds h2,
  .rekindle-minds .h2 {
    margin-top: 25px;
  }
  .rekindle-minds .mt-7 {
    margin-top: 2rem;
  }
  .rekindle-minds .ribbon-block {
    margin-bottom: 265px;
    margin-top: 20px;
  }
  .rekindle-minds .rekindle-carousel .carousel .review-image .right {
    right: 5px;
  }
  .rekindle-minds .rekindle-carousel .carousel .review-image {
    transform: scale(0.9);
    margin-top: -15px;
  }
  .rekindle-minds .carousel-root {
    padding-top: 25px;
  }
  .rekindle-minds .rekindle-minds .right-block {
    margin-top: -10px;
    padding-bottom: 70px;
  }
  .rekindle-minds .player-container {
    margin-top: -250px;
  }
}

/* iPad */
@media all and (device-width: 1024px) and (device-height: 768px) and (orientation: landscape) {
  .rekindle-minds .navbar .nav-link {
    margin: 0 5px;
  }
}

/* iPad Pro*/
@media all and (device-width: 1366px) and (device-height: 1024px) and (orientation: landscape) {
}
